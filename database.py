"""
Database schema and utilities for x2shmot project.
"""

import sqlite3

# Database schema definitions
CREATE_ITEMS_TABLE = """
CREATE TABLE IF NOT EXISTS items (
	id INTEGER PRIMARY KEY AUTOINCREMENT,
	part_type TEXT,
	dlc_name TEXT,
	template_name TEXT UNIQUE,
	gender TEXT,
	armor_template TEXT,
	display_name TEXT
);
"""

CREATE_SOLDIERS_TABLE = """
CREATE TABLE IF NOT EXISTS soldiers (
	id INTEGER PRIMARY KEY AUTOINCREMENT,
	name TEXT,
	gender TEXT,
	armor_type TEXT
);
"""

CREATE_SOLDIER_ITEMS_TABLE = """
CREATE TABLE IF NOT EXISTS soldier_items (
	soldier_id INTEGER,
	part_type TEXT,
	item_id INTEGER,
	FOREIGN KEY(soldier_id) REFERENCES soldiers(id),
	FOREIGN KEY(item_id) REFERENCES items(id),
	PRIMARY KEY(soldier_id, part_type)
);
"""


def get_database_connection(db_path="x2shmot.db"):
	"""
	Get a connection to the SQLite database.

	Args:
		db_path (str): Path to the database file

	Returns:
		sqlite3.Connection: Database connection object
	"""
	return sqlite3.connect(db_path)


def initialize_database(db_path="x2shmot.db"):
	"""
	Initialize the database with the required tables.

	Args:
		db_path (str): Path to the database file
	"""
	conn = get_database_connection(db_path)
	cursor = conn.cursor()

	try:
		# Create tables
		cursor.execute(CREATE_ITEMS_TABLE)
		cursor.execute(CREATE_SOLDIERS_TABLE)
		cursor.execute(CREATE_SOLDIER_ITEMS_TABLE)

		conn.commit()
		print(f"Database initialized successfully at {db_path}")

	except sqlite3.Error as e:
		print(f"Error initializing database: {e}")
		conn.rollback()

	finally:
		conn.close()


def get_all_items(db_path="x2shmot.db"):
	"""
	Retrieve all items from the database.

	Args:
		db_path (str): Path to the database file

	Returns:
		list: List of item records
	"""
	conn = get_database_connection(db_path)
	cursor = conn.cursor()

	try:
		cursor.execute("SELECT * FROM items")
		items = cursor.fetchall()
		return items

	except sqlite3.Error as e:
		print(f"Error retrieving items: {e}")
		return []

	finally:
		conn.close()


def get_all_soldiers(db_path="x2shmot.db"):
	"""
	Retrieve all soldiers from the database.

	Args:
		db_path (str): Path to the database file

	Returns:
		list: List of soldier records
	"""
	conn = get_database_connection(db_path)
	cursor = conn.cursor()

	try:
		cursor.execute("SELECT * FROM soldiers")
		soldiers = cursor.fetchall()
		return soldiers

	except sqlite3.Error as e:
		print(f"Error retrieving soldiers: {e}")
		return []

	finally:
		conn.close()


if __name__ == "__main__":
	# Initialize database when script is run directly
	initialize_database()
