CREATE TABLE items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    part_type TEXT,
    dlc_name TEXT,
    template_name TEXT UNIQUE,
    gender TEXT,
    armor_template TEXT,
    display_name TEXT
);

CREATE TABLE soldiers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    gender TEXT,
    armor_type TEXT
);

CREATE TABLE soldier_items (
    soldier_id INTEGER,
    part_type TEXT,
    item_id INTEGER,
    FOREIGN KEY(soldier_id) REFERENCES soldiers(id),
    FOREIGN KEY(item_id) REFERENCES items(id),
    PRIMARY KEY(soldier_id, part_type)
);