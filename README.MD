# X2SHMOT Project
## About
X2SHMOT is a database management system for tracking soldiers and their equipment items. The project provides utilities for creating and managing a SQLite database that stores information about soldiers, equipment items, and their relationships.
## Code ConventionsFor AI helpers and contributors:
1. We use line comments to comment code with `#`
2. We use tab indentation for all Python files





There's XCOM 2 game. You have soldiers that can be customized (name, armor colors, hairstyle, etc.). There's a lot of mods that bring in custom style – custom clothes, hair, etc. Each mod has its own folder. In it there's file XComContent.ini which has list of all cospetic items added; and XComGame.int where there are readable names for all added items. 

For example in XComContent.ini there's this line:
+BodyPartTemplateConfig=(PartType="Torso", DLCName="XCOM_Hero_Suits", TemplateName="FHU_CNV_Torso_M", ArchetypeName="FHUDecos.Archetypes.FHU_CNV_Torso_M", Gender=eGender_male, bVeteran=false,  ArmorTemplate="KevlarArmor", CharacterTemplate="Soldier")

It means that:
• PartType="Torso" – item goes into soldier's torso slot. other options: TorsoDeco, Shins, LeftArm, etc.
• DLCName="XCOM_Hero_Suits": name of the mod (DLC)
• TemplateName="FHU_CNV_Torso_M" – this is unique name of the item
• Gender=eGender_male – gender. item is either for a male or female soldier. For female it's Gender=eGender_Female
• ArmorTemplate="KevlarArmor" – means for which armor is current item. In game there are different armors like kevlar armor, powered armor, predator armor, etc. So if soldier wears kevlar armor in game, then this item is allowed to be used as cosmetic outlook, otherwise not.
• CharacterTemplate="Soldier" – if there's no such declaration, then the current item is not for soldiers, which means it should be ignored

In XComGame.int there's a corresponding input:
[FHU_CNV_Torso_M X2BodyPartTemplate]
DisplayName="X-Com Hero Conventional"

It means that for this item in game name "X-Com Hero Conventional" will show.

I want to make an app that will scan all mod folders and create a database of all custom items. Then there should be interface to create a soldier give it a name, customzie its appearance based on the items from mods and save for later usage. Its intended for my personal usage.

Two tasks for you:
1. Pleas describe what should be my technology stack for this project? 
2. Please provide a detailed step by step roadmap on implementation





Thoughts
Expand for details
Below is a detailed response to your query about creating an app for XCOM 2 mod management and soldier customization. I’ve broken it down into the two tasks you requested: the technology stack and a step-by-step implementation roadmap.

Task 1: Technology Stack
To build an app that scans XCOM 2 mod folders, extracts cosmetic item data, and provides a user interface for customizing soldiers, the following technology stack is recommended for your personal use case:

Programming Language: Python 3
Python is ideal due to its simplicity, readability, and extensive libraries for file parsing and database management. It’s perfect for a personal project where you want quick development without unnecessary complexity.
GUI Framework: Tkinter
Tkinter is Python’s built-in GUI library, making it lightweight and easy to use for a desktop application. Since this is for personal use and involves local file access, a desktop app with Tkinter is straightforward and sufficient. It allows you to create forms, dropdowns, and buttons without additional installations.
Database: SQLite
SQLite is a lightweight, file-based relational database included in Python’s standard library (via the sqlite3 module). It’s perfect for personal use as it doesn’t require a separate server, and it can efficiently store and query your mod items and soldier customizations.
Libraries:
configparser: Included in Python’s standard library, this will parse the XComGame.int files, which follow a section-based .ini-like format.
re (Regular Expressions): Also in the standard library, this will help parse the custom format of XComContent.ini files, which use lines starting with + and key-value pairs in parentheses.
sqlite3: Included in Python, this will handle all database operations like creating tables, inserting data, and querying items.
This stack keeps the project simple, self-contained, and aligned with your personal use needs. You won’t need external dependencies beyond Python itself, and the desktop app approach suits the requirement of scanning local mod folders.

Task 2: Step-by-Step Implementation Roadmap
Here’s a detailed roadmap to implement your app, from setup to testing. Each step builds on the previous one, ensuring a structured development process.

Step 1: Set Up the Project
Objective: Establish the project foundation.
Actions:
Create a new directory for your project (e.g., xcom2_mod_customizer).
Inside this directory, set up a Python virtual environment to keep dependencies isolated:
bash

Collapse

Wrap

Run

Copy
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
Ensure Python 3 is installed on your system (Tkinter, configparser, re, and sqlite3 come with it, so no additional installs are needed).
Create three main files: main.py (GUI and logic), parser.py (file parsing), and database.py (database operations).
Step 2: Design the Database Schema
Objective: Create a database to store mod items and soldier customizations.
Actions:
In database.py, write a function to initialize an SQLite database (e.g., xcom2_mods.db) in the project directory.
Define three tables:
items:
sql

Collapse

Wrap

Copy
CREATE TABLE items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    part_type TEXT,
    dlc_name TEXT,
    template_name TEXT UNIQUE,
    gender TEXT,
    armor_template TEXT,
    display_name TEXT
);
Stores mod items with columns for all relevant data from XComContent.ini and XComGame.int.
template_name is unique to avoid duplicates across mods.
soldiers:
sql

Collapse

Wrap

Copy
CREATE TABLE soldiers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT,
    gender TEXT,
    armor_type TEXT
);
Stores soldier details like name, gender, and selected armor type.
soldier_items:
sql

Collapse

Wrap

Copy
CREATE TABLE soldier_items (
    soldier_id INTEGER,
    part_type TEXT,
    item_id INTEGER,
    FOREIGN KEY(soldier_id) REFERENCES soldiers(id),
    FOREIGN KEY(item_id) REFERENCES items(id),
    PRIMARY KEY(soldier_id, part_type)
);
Links soldiers to their selected items, ensuring one item per part type per soldier.
Example code in database.py:
python

Collapse

Wrap

Run

Copy
import sqlite3

def init_db():
    conn = sqlite3.connect("xcom2_mods.db")
    cursor = conn.cursor()
    cursor.execute('''CREATE TABLE IF NOT EXISTS items (...)''')  # Full SQL as above
    cursor.execute('''CREATE TABLE IF NOT EXISTS soldiers (...)''')
    cursor.execute('''CREATE TABLE IF NOT EXISTS soldier_items (...)''')
    conn.commit()
    conn.close()
Step 3: Implement File Parsing
Objective: Extract item data from mod files.
Actions:
In parser.py, create two functions:
parse_xcom_content_ini(file_path):
Reads XComContent.ini line by line.
Uses regex to match lines like +BodyPartTemplateConfig=(...):
python

Collapse

Wrap

Run

Copy
import re

def parse_xcom_content_ini(file_path):
    items = []
    with open(file_path, 'r') as f:
        for line in f:
            if line.startswith('+BodyPartTemplateConfig='):
                match = re.search(r'\((.*?)\)', line)
                if match:
                    params = dict(re.findall(r'(\w+)="?(.*?)"?(?:,|$)', match.group(1)))
                    if params.get('CharacterTemplate') == 'Soldier':
                        item = {
                            'part_type': params['PartType'],
                            'dlc_name': params['DLCName'],
                            'template_name': params['TemplateName'],
                            'gender': 'male' if params['Gender'] == 'eGender_male' else 'female',
                            'armor_template': params['ArmorTemplate']
                        }
                        items.append(item)
    return items
Returns a list of item dictionaries for soldiers.
parse_xcom_game_int(file_path):
Uses configparser to read XComGame.int:
python

Collapse

Wrap

Run

Copy
import configparser

def parse_xcom_game_int(file_path):
    config = configparser.ConfigParser()
    config.read(file_path)
    display_names = {}
    for section in config.sections():
        if section.endswith(' X2BodyPartTemplate'):
            template_name = section.split()[0]
            display_names[template_name] = config[section].get('DisplayName', template_name)
    return display_names
Returns a dictionary mapping template_name to DisplayName.
Step 4: Implement Mod Scanning
Objective: Scan mod folders and populate the database.
Actions:
In main.py, create a scan_mods(directory) function:
python

Collapse

Wrap

Run

Copy
import os
from parser import parse_xcom_content_ini, parse_xcom_game_int
from database import init_db, insert_items

def scan_mods(directory):
    init_db()
    conn = sqlite3.connect("xcom2_mods.db")
    cursor = conn.cursor()
    cursor.execute("DELETE FROM items")  # Clear existing items
    for mod_folder in os.listdir(directory):
        mod_path = os.path.join(directory, mod_folder)
        if os.path.isdir(mod_path):
            content_file = os.path.join(mod_path, 'XComContent.ini')
            game_file = os.path.join(mod_path, 'XComGame.int')
            items = []
            if os.path.exists(content_file):
                items = parse_xcom_content_ini(content_file)
            if os.path.exists(game_file):
                display_names = parse_xcom_game_int(game_file)
                for item in items:
                    item['display_name'] = display_names.get(item['template_name'], item['template_name'])
                    cursor.execute("""
                        INSERT OR REPLACE INTO items (part_type, dlc_name, template_name, gender, armor_template, display_name)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (item['part_type'], item['dlc_name'], item['template_name'],
                          item['gender'], item['armor_template'], item['display_name']))
    conn.commit()
    conn.close()
Scans subfolders, parses files, and inserts items into the database, matching display names where available.
Step 5: Develop the GUI
Objective: Build a user-friendly interface with Tkinter.
Actions:
In main.py, set up the main window:
python

Collapse

Wrap

Run

Copy
import tkinter as tk
from tkinter import filedialog, ttk

root = tk.Tk()
root.title("XCOM 2 Soldier Customizer")

tk.Button(root, text="Scan Mods", command=lambda: scan_mods(filedialog.askdirectory())).pack()
tk.Button(root, text="Create New Soldier", command=create_soldier_form).pack()
tk.Button(root, text="Load Soldier", command=load_soldier_list).pack()

root.mainloop()
Implement create_soldier_form():
Opens a new window with fields for name, gender, armor type, and item selection per part type.
Populates dropdowns dynamically from the database (see Step 6).
Implement load_soldier_list():
Shows a list of saved soldiers to select and edit.
Step 6: Implement Soldier Customization
Objective: Allow soldier creation and item selection.
Actions:
In create_soldier_form(), query the database for options:
python

Collapse

Wrap

Run

Copy
def create_soldier_form():
    form = tk.Toplevel()
    form.title("Create Soldier")

    tk.Label(form, text="Name:").pack()
    name_entry = tk.Entry(form)
    name_entry.pack()

    tk.Label(form, text="Gender:").pack()
    gender_var = tk.StringVar(value="male")
    ttk.Combobox(form, textvariable=gender_var, values=["male", "female"]).pack()

    tk.Label(form, text="Armor Type:").pack()
    conn = sqlite3.connect("xcom2_mods.db")
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT armor_template FROM items")
    armor_types = [row[0] for row in cursor.fetchall()]
    armor_var = tk.StringVar(value=armor_types[0] if armor_types else "")
    ttk.Combobox(form, textvariable=armor_var, values=armor_types).pack()

    item_vars = {}
    cursor.execute("SELECT DISTINCT part_type FROM items")
    part_types = [row[0] for row in cursor.fetchall()]
    for part in part_types:
        tk.Label(form, text=f"{part}:").pack()
        cursor.execute("SELECT id, display_name FROM items WHERE gender=? AND armor_template=? AND part_type=?",
                       (gender_var.get(), armor_var.get(), part))
        items = cursor.fetchall()
        item_var = tk.StringVar(value=items[0][1] if items else "None")
        ttk.Combobox(form, textvariable=item_var, values=[item[1] for item in items]).pack()
        item_vars[part] = (item_var, items)
    
    def save_soldier():
        cursor.execute("INSERT INTO soldiers (name, gender, armor_type) VALUES (?, ?, ?)",
                       (name_entry.get(), gender_var.get(), armor_var.get()))
        soldier_id = cursor.lastrowid
        for part, (var, items) in item_vars.items():
            item_id = next((i[0] for i in items if i[1] == var.get()), None)
            if item_id:
                cursor.execute("INSERT INTO soldier_items (soldier_id, part_type, item_id) VALUES (?, ?, ?)",
                               (soldier_id, part, item_id))
        conn.commit()
        form.destroy()

    tk.Button(form, text="Save", command=save_soldier).pack()
    conn.close()
Updates item dropdowns based on gender and armor type selections.
Step 7: Add Functionality to Load and Edit Soldiers
Objective: Enable loading and modifying saved soldiers.
Actions:
In load_soldier_list():
python

Collapse

Wrap

Run

Copy
def load_soldier_list():
    list_window = tk.Toplevel()
    list_window.title("Load Soldier")
    conn = sqlite3.connect("xcom2_mods.db")
    cursor = conn.cursor()
    cursor.execute("SELECT id, name FROM soldiers")
    soldiers = cursor.fetchall()
    for sid, sname in soldiers:
        tk.Button(list_window, text=sname, command=lambda s=sid: edit_soldier(s)).pack()
    conn.close()

def edit_soldier(soldier_id):
    # Similar to create_soldier_form, but loads existing data
    # Populates fields and dropdowns with saved values
    # Saves updates to the same soldier_id
Loads soldier data and selected items for editing.
Step 8: Enhance User Experience
Objective: Make the app robust and intuitive.
Actions:
Add error handling in scan_mods() for missing files or parsing errors.
Show a message box (e.g., tk.messagebox.showinfo) after scanning or saving.
Validate that name, gender, and armor type are selected before saving.
Step 9: Test the Application
Objective: Ensure everything works as expected.
Actions:
Create sample mod folders with XComContent.ini and XComGame.int files (use your example data).
Test scanning, soldier creation, saving, loading, and editing.
Verify that item dropdowns filter correctly by gender and armor type.
Step 10: Refine and Finalize
Objective: Polish the app based on testing.
Actions:
Adjust GUI layout for clarity (e.g., use frames or grids).
Add tooltips or labels if needed.
Fix any bugs or edge cases (e.g., no items for a part type).
Summary
With this technology stack (Python 3, Tkinter, SQLite, configparser, re) and roadmap, you’ll have a functional desktop app that:

Scans your XCOM 2 mod folders to catalog custom cosmetic items.
Lets you create and customize soldiers with a simple GUI.
Saves and loads your customizations for personal use.
The app stays lightweight and tailored to your needs, leveraging Python’s strengths for quick development and local file handling. Start with Step 1, and build incrementally—happy coding!